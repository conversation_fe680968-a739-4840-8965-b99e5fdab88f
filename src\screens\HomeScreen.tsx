import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  SafeAreaView,
  RefreshControl,
} from 'react-native';
import { theme } from '../../theme';
import { getExpenses, Expense } from '../utils/storage';

const formatCurrency = (amount: number) =>
  new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);

const ExpenseItem = ({ item }: { item: Expense }) => (
  <View style={styles.item}>
    <Text style={styles.emoji}>{item.topicTag}</Text>
    <View style={styles.details}>
      <Text style={styles.amount}>{formatCurrency(item.amount)}</Text>
      <Text style={styles.note}>
        {item.note || 'No note'}
      </Text>
    </View>
    <Text style={styles.time}>
      {new Date(item.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
    </Text>
  </View>
);

export default function HomeScreen() {
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  const loadExpenses = async () => {
    const data = await getExpenses();
    setExpenses(data);
  };

  useEffect(() => {
    loadExpenses();
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadExpenses();
    setRefreshing(false);
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>💸 Spenny</Text>
        <Text style={styles.subtitle}>Track with joy 😊</Text>
      </View>

      <FlatList
        data={expenses}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => <ExpenseItem item={item} />}
        contentContainerStyle={styles.list}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={[theme.primary]} />
        }
        ListEmptyComponent={
          <Text style={styles.empty}>No expenses yet! Ready to log your first coffee? ☕</Text>
        }
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: theme.bg },
  header: { padding: 20, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: '700', color: theme.primary },
  subtitle: { fontSize: 16, color: '#888', marginTop: 4 },
  list: { paddingHorizontal: 16, paddingBottom: 20 },
  item: {
    backgroundColor: theme.card,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  emoji: { fontSize: 28, marginRight: 12 },
  details: { flex: 1 },
  amount: { fontSize: 18, fontWeight: '600', color: theme.text },
  note: { fontSize: 14, color: '#777' },
  time: { fontSize: 12, color: '#aaa', textAlign: 'right', minWidth: 50 },
  empty: { textAlign: 'center', color: '#aaa', marginTop: 40, fontStyle: 'italic' },
});