import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { theme } from '../../theme';

export default function ReportsScreen() {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>📊 Monthly Insights</Text>
      <Text style={styles.tip}>Coming soon: Emoji-powered spending pie charts! 🥧</Text>
      <Text style={styles.fun}>You’ve logged 0 expenses this month. Go treat yourself! 🎉</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20, backgroundColor: theme.bg },
  title: { fontSize: 22, fontWeight: 'bold', color: theme.primary, marginBottom: 12 },
  tip: { fontSize: 16, color: '#888', textAlign: 'center', marginBottom: 10 },
  fun: { fontSize: 16, color: theme.accent, fontStyle: 'italic' },
});