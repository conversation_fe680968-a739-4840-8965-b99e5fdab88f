import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Keyboard,
  TouchableWithoutFeedback,
  Alert,
  StyleSheet,
} from 'react-native';
import { theme } from '../../theme';
import { saveExpense } from '../utils/storage';
import uuid from 'react-native-uuid';

const SUGGESTED_TAGS = ['☕Cof', '🛒Gro', '🚌Tra', '💪Gym', '🍕Eat', '🏠Rent', '🎉Fun', '📚Edu'];

export default function AddExpenseScreen() {
  const [amount, setAmount] = useState('');
  const [topicTag, setTopicTag] = useState('💸Pay');
  const [note, setNote] = useState('');
  const [customTag, setCustomTag] = useState('');

  const handleSave = () => {
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount) || numAmount <= 0) {
      Alert.alert('Invalid amount', 'Please enter a valid amount.');
      return;
    }

    const newExpense = {
      id: uuid.v4() as string,
      amount: numAmount,
      topicTag: topicTag,
      note: note || undefined,
      timestamp: Date.now(),
    };

    saveExpense(newExpense);
    Alert.alert('Saved!', `${topicTag} expense added.`);
    setAmount('');
    setNote('');
    setTopicTag('💸Pay');
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View style={styles.container}>
        <Text style={styles.title}>New Expense</Text>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Amount ($)</Text>
          <TextInput
            style={styles.input}
            value={amount}
            onChangeText={setAmount}
            placeholder="0.00"
            keyboardType="decimal-pad"
            placeholderTextColor="#ccc"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Topic Tag</Text>
          <TextInput
            style={[styles.input, { fontSize: 20 }]}
            value={topicTag}
            onChangeText={setTopicTag}
            placeholder="e.g., ☕Cof"
          />
        </View>

        <View style={styles.suggestions}>
          {SUGGESTED_TAGS.map((tag) => (
            <TouchableOpacity
              key={tag}
              onPress={() => setTopicTag(tag)}
              style={styles.tagButton}
            >
              <Text style={styles.tagText}>{tag}</Text>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Note (optional)</Text>
          <TextInput
            style={styles.input}
            value={note}
            onChangeText={setNote}
            placeholder="e.g., Latte with Jess"
            placeholderTextColor="#ccc"
          />
        </View>

        <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
          <Text style={styles.saveText}>💾 Save Expense</Text>
        </TouchableOpacity>
      </View>
    </TouchableWithoutFeedback>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, padding: 20, backgroundColor: theme.bg },
  title: { fontSize: 24, fontWeight: 'bold', color: theme.primary, marginBottom: 20, textAlign: 'center' },
  inputGroup: { marginBottom: 16 },
  label: { fontSize: 16, color: theme.text, marginBottom: 6, fontWeight: '500' },
  input: {
    backgroundColor: '#fff',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 10,
    fontSize: 16,
    borderWidth: 1,
    borderColor: theme.border,
  },
  suggestions: { flexDirection: 'row', flexWrap: 'wrap', gap: 8, marginVertical: 12 },
  tagButton: {
    backgroundColor: theme.card,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: theme.border,
  },
  tagText: { fontSize: 16 },
  saveButton: {
    marginTop: 20,
    backgroundColor: theme.accent,
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
  },
  saveText: { color: '#fff', fontSize: 18, fontWeight: '600' },
});