/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */
import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';

import HomeScreen from './src/screens/HomeScreen';
import AddExpenseScreen from './src/screens/AddExpenseScreen';
import ReportsScreen from './src/screens/ReportsScreen';

import { StyleSheet, View } from 'react-native';
import { theme } from './theme';

const Tab = createBottomTabNavigator();

export default function App() {
  return (
    <NavigationContainer>
      <Tab.Navigator
        screenOptions={({ route }) => ({
          tabBarIcon: ({ focused, color, size }) => {
            let iconName = 'help';

            if (route.name === 'Home') iconName = focused ? 'home' : 'home-outline';
            else if (route.name === 'Add') iconName = 'add-circle';
            else if (route.name === 'Reports') iconName = focused ? 'pie-chart' : 'pie-chart-outline';

            return <Ionicons name={iconName} size={size} color={color} />;
          },
          tabBarActiveTintColor: theme.primary,
          tabBarInactiveTintColor: '#ccc',
          tabBarStyle: styles.tabBar,
          headerStyle: { backgroundColor: theme.bg },
          headerTintColor: theme.text,
          headerTitleStyle: { fontWeight: '600' },
        })}
      >
        <Tab.Screen name="Home" component={HomeScreen} options={{ title: 'Expenses' }} />
        <Tab.Screen
          name="Add"
          component={AddExpenseScreen}
          options={{
            title: 'Add Expense',
            tabBarIcon: ({ color }) => (
              <Ionicons name="add-circle" size={60} color={theme.accent} />
            ),
            tabBarButton: (props) => (
              <View style={styles.fabButton}>
                <Ionicons name="add" size={36} color="#fff" />
              </View>
            ),
          }}
        />
        <Tab.Screen name="Reports" component={ReportsScreen} options={{ title: 'Insights' }} />
      </Tab.Navigator>
    </NavigationContainer>
  );
}

const styles = StyleSheet.create({
  tabBar: {
    backgroundColor: theme.bg,
    borderTopWidth: 0,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  fabButton: {
    backgroundColor: theme.accent,
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: -10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
  },
});
