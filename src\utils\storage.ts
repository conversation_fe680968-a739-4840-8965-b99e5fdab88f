import AsyncStorage from '@react-native-async-storage/async-storage';

const STORAGE_KEY = '@spenny_expenses';

export type Expense = {
  id: string;
  amount: number;
  topicTag: string; // e.g., "☕Cof"
  note?: string;
  timestamp: number;
};

export const saveExpense = async (expense: Expense) => {
  try {
    const existing = await getExpenses();
    const updated = [expense, ...existing];
    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(updated));
  } catch (error) {
    console.warn('Failed to save expense', error);
  }
};

export const getExpenses = async (): Promise<Expense[]> => {
  try {
    const value = await AsyncStorage.getItem(STORAGE_KEY);
    return value ? JSON.parse(value) : [];
  } catch (error) {
    console.warn('Failed to load expenses', error);
    return [];
  }
};